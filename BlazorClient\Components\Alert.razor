@if (IsVisible)
{
    <div class="alert alert-@Type @(IsDismissible ? "alert-dismissible" : "") fade show" role="alert">
        @if (!string.IsNullOrEmpty(Icon))
        {
            <i class="bi bi-@Icon me-2"></i>
        }
        
        @if (!string.IsNullOrEmpty(Title))
        {
            <h6 class="alert-heading">@Title</h6>
        }
        
        @if (ChildContent != null)
        {
            @ChildContent
        }
        else
        {
            @Message
        }
        
        @if (IsDismissible)
        {
            <button type="button" class="btn-close" @onclick="Dismiss" aria-label="Close"></button>
        }
    </div>
}

@code {
    [Parameter] public bool IsVisible { get; set; } = true;
    [Parameter] public string Type { get; set; } = "info"; // primary, secondary, success, danger, warning, info, light, dark
    [Parameter] public string? Title { get; set; }
    [Parameter] public string? Message { get; set; }
    [Parameter] public string? Icon { get; set; }
    [Parameter] public bool IsDismissible { get; set; } = true;
    [Parameter] public RenderFragment? ChildContent { get; set; }
    [Parameter] public EventCallback OnDismiss { get; set; }

    private async Task Dismiss()
    {
        IsVisible = false;
        await OnDismiss.InvokeAsync();
        StateHasChanged();
    }
}
