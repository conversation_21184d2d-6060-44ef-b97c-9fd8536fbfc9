﻿<Router AppAssembly="@typeof(App).Assembly">
    <Found Context="routeData">
        <RouteView RouteData="@routeData" DefaultLayout="@typeof(MainLayout)" />
        <FocusOnNavigate RouteData="@routeData" Selector="h1" />
    </Found>
    <NotFound>
        <PageTitle>Страница не найдена</PageTitle>
        <LayoutView Layout="@typeof(MainLayout)">
            <div class="container">
                <div class="row">
                    <div class="col-12 text-center">
                        <h1 class="display-1">404</h1>
                        <h2>Страница не найдена</h2>
                        <p class="lead">К сожалению, запрашиваемая страница не существует.</p>
                        <a href="/" class="btn btn-primary">Вернуться на главную</a>
                    </div>
                </div>
            </div>
        </LayoutView>
    </NotFound>
</Router>
