# 🔄 Руководство по миграции с SQLite на PostgreSQL

## 📋 Обзор изменений

Приложение было обновлено для работы с PostgreSQL вместо SQLite. Это обеспечивает:

- ✅ **Лучшую производительность** для больших объемов данных
- ✅ **Расширенные возможности** SQL
- ✅ **Лучшую поддержку** многопользовательского режима
- ✅ **Промышленную надежность**

## 🔧 Что изменилось

### 1. Пакеты NuGet
```diff
- Microsoft.EntityFrameworkCore.Sqlite
+ Npgsql.EntityFrameworkCore.PostgreSQL
```

### 2. Строка подключения
```diff
- "Data Source=products.db"
+ "Host=localhost;Database=ProductManagement;Username=********;Password=********;Port=5432"
```

### 3. Конфигурация DbContext
```diff
- options.UseSqlite(connectionString)
+ options.UseNpgsql(connectionString)
```

### 4. Настройки модели
```diff
- entity.Property(e => e.CreatedDate).HasDefaultValueSql("datetime('now')");
+ entity.Property(e => e.CreatedDate).HasDefaultValueSql("CURRENT_TIMESTAMP");
+ entity.Property(e => e.Id).UseIdentityByDefaultColumn();
```

## 🚀 Быстрая настройка

### Вариант 1: Docker (рекомендуется)
```powershell
# 1. Запустите PostgreSQL
.\start-********.ps1

# 2. Запустите приложение
.\start-app.ps1
```

### Вариант 2: Локальная установка
```powershell
# 1. Установите PostgreSQL
# Скачайте с https://www.********ql.org/download/

# 2. Настройте базу данных
.\setup-********ql.ps1

# 3. Запустите приложение
.\start-app.ps1
```

## 📊 Миграция данных (если нужно)

Если у вас есть данные в SQLite, которые нужно перенести:

### 1. Экспорт из SQLite
```bash
# Подключитесь к старой базе SQLite
sqlite3 products.db

# Экспортируйте данные
.mode csv
.headers on
.output products_export.csv
SELECT * FROM Products;
.quit
```

### 2. Импорт в PostgreSQL
```sql
-- Подключитесь к PostgreSQL
psql -h localhost -U ******** -d ProductManagement

-- Создайте временную таблицу
CREATE TEMP TABLE temp_products (
    Id INTEGER,
    Name VARCHAR(100),
    Description VARCHAR(500),
    Price DECIMAL(18,2),
    Category VARCHAR(50),
    InStock BOOLEAN,
    CreatedDate TIMESTAMP
);

-- Импортируйте данные
\copy temp_products FROM 'products_export.csv' WITH CSV HEADER;

-- Перенесите в основную таблицу (пропуская ID для автоинкремента)
INSERT INTO "Products" (Name, Description, Price, Category, InStock, CreatedDate)
SELECT Name, Description, Price, Category, InStock, CreatedDate
FROM temp_products;
```

## 🔍 Проверка миграции

### 1. Проверьте подключение
```powershell
# Проверьте статус PostgreSQL
docker ps | findstr ********

# Или для локальной установки
Get-Service ********ql*
```

### 2. Проверьте базу данных
```sql
-- Подключитесь к базе
psql -h localhost -U ******** -d ProductManagement

-- Проверьте таблицы
\dt

-- Проверьте данные
SELECT COUNT(*) FROM "Products";
SELECT * FROM "Products" LIMIT 5;
```

### 3. Проверьте приложение
1. Запустите сервер: `cd BlazorServer && dotnet run`
2. Откройте API: https://localhost:7297/api/products
3. Проверьте, что данные отображаются

## 🛠️ Устранение проблем

### Проблема: "Connection refused"
```powershell
# Запустите PostgreSQL
.\start-********.ps1

# Или проверьте Docker
docker-compose up -d
```

### Проблема: "Database does not exist"
```sql
-- Создайте базу данных
psql -h localhost -U ******** -c "CREATE DATABASE \"ProductManagement\";"
```

### Проблема: Ошибки миграций
```powershell
# Удалите старые миграции
Remove-Item -Recurse BlazorServer\Migrations

# Создайте новые
cd BlazorServer
dotnet ef migrations add InitialCreate
dotnet ef database update
```

## 📈 Преимущества PostgreSQL

### Производительность
- **Индексы**: Более эффективные B-tree, Hash, GiST индексы
- **Параллелизм**: Лучшая поддержка многопользовательского доступа
- **Кэширование**: Встроенное кэширование запросов

### Функциональность
- **JSON поддержка**: Нативная работа с JSON данными
- **Полнотекстовый поиск**: Встроенные возможности поиска
- **Расширения**: Множество доступных расширений

### Надежность
- **ACID**: Полная поддержка транзакций
- **Репликация**: Встроенные возможности репликации
- **Бэкапы**: Продвинутые инструменты резервного копирования

## 🔄 Откат к SQLite (если нужно)

Если по какой-то причине нужно вернуться к SQLite:

### 1. Измените пакет
```xml
<PackageReference Include="Microsoft.EntityFrameworkCore.Sqlite" Version="9.0.2" />
```

### 2. Измените строку подключения
```json
"DefaultConnection": "Data Source=products.db"
```

### 3. Измените конфигурацию
```csharp
options.UseSqlite(connectionString)
```

### 4. Обновите модель
```csharp
entity.Property(e => e.CreatedDate).HasDefaultValueSql("datetime('now')");
```

## 📞 Поддержка

Если возникли проблемы с миграцией:

1. **Проверьте TROUBLESHOOTING.md** - там есть решения частых проблем
2. **Проверьте логи** сервера на наличие ошибок подключения
3. **Убедитесь**, что PostgreSQL запущен и доступен
4. **Пересоздайте миграции** если есть ошибки схемы

## ✅ Контрольный список

- [ ] PostgreSQL установлен и запущен
- [ ] База данных ProductManagement создана
- [ ] Пользователь ******** настроен
- [ ] Миграции применены успешно
- [ ] Тестовые данные загружены
- [ ] Приложение подключается к базе
- [ ] API возвращает данные
- [ ] Веб-интерфейс работает корректно

Миграция завершена! 🎉
