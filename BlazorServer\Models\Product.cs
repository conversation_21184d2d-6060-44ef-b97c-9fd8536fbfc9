using System.ComponentModel.DataAnnotations;

namespace BlazorServer.Models;

public class Product
{
    public int Id { get; set; }

    [Required(ErrorMessage = "Название товара обязательно")]
    [StringLength(100, ErrorMessage = "Название товара не может превышать 100 символов")]
    public string Name { get; set; } = string.Empty;

    [StringLength(500, ErrorMessage = "Описание не может превышать 500 символов")]
    public string Description { get; set; } = string.Empty;

    [Required(ErrorMessage = "Цена обязательна")]
    [Range(0.01, double.MaxValue, ErrorMessage = "Цена должна быть больше 0")]
    public decimal Price { get; set; }

    [Required(ErrorMessage = "Категория обязательна")]
    [StringLength(50, ErrorMessage = "Категория не может превышать 50 символов")]
    public string Category { get; set; } = string.Empty;

    public bool InStock { get; set; } = true;

    public DateTime CreatedDate { get; set; } = DateTime.UtcNow;
}
