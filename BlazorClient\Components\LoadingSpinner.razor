@if (IsVisible)
{
    <div class="d-flex justify-content-center align-items-center" style="min-height: @MinHeight;">
        <div class="text-center">
            <div class="spinner-border text-@Color" role="status" style="width: @Size; height: @Size;">
                <span class="visually-hidden">@Text</span>
            </div>
            @if (!string.IsNullOrEmpty(Text))
            {
                <div class="mt-2">
                    <small class="text-muted">@Text</small>
                </div>
            }
        </div>
    </div>
}

@code {
    [Parameter] public bool IsVisible { get; set; } = true;
    [Parameter] public string Text { get; set; } = "Загрузка...";
    [Parameter] public string Color { get; set; } = "primary";
    [Parameter] public string Size { get; set; } = "3rem";
    [Parameter] public string MinHeight { get; set; } = "200px";
}
