/* /Layout/MainLayout.razor.rz.scp.css */
.page[b-aa2zzlyc8p] {
    position: relative;
    display: flex;
    flex-direction: column;
}

main[b-aa2zzlyc8p] {
    flex: 1;
}

.sidebar[b-aa2zzlyc8p] {
    background-image: linear-gradient(180deg, rgb(5, 39, 103) 0%, #3a0647 70%);
}

.top-row[b-aa2zzlyc8p] {
    background-color: #f7f7f7;
    border-bottom: 1px solid #d6d5d5;
    justify-content: flex-end;
    height: 3.5rem;
    display: flex;
    align-items: center;
}

    .top-row[b-aa2zzlyc8p]  a, .top-row[b-aa2zzlyc8p]  .btn-link {
        white-space: nowrap;
        margin-left: 1.5rem;
        text-decoration: none;
    }

    .top-row[b-aa2zzlyc8p]  a:hover, .top-row[b-aa2zzlyc8p]  .btn-link:hover {
        text-decoration: underline;
    }

    .top-row[b-aa2zzlyc8p]  a:first-child {
        overflow: hidden;
        text-overflow: ellipsis;
    }

@media (max-width: 640.98px) {
    .top-row[b-aa2zzlyc8p] {
        justify-content: space-between;
    }

    .top-row[b-aa2zzlyc8p]  a, .top-row[b-aa2zzlyc8p]  .btn-link {
        margin-left: 0;
    }
}

@media (min-width: 641px) {
    .page[b-aa2zzlyc8p] {
        flex-direction: row;
    }

    .sidebar[b-aa2zzlyc8p] {
        width: 250px;
        height: 100vh;
        position: sticky;
        top: 0;
    }

    .top-row[b-aa2zzlyc8p] {
        position: sticky;
        top: 0;
        z-index: 1;
    }

    .top-row.auth[b-aa2zzlyc8p]  a:first-child {
        flex: 1;
        text-align: right;
        width: 0;
    }

    .top-row[b-aa2zzlyc8p], article[b-aa2zzlyc8p] {
        padding-left: 2rem !important;
        padding-right: 1.5rem !important;
    }
}
/* /Layout/NavMenu.razor.rz.scp.css */
.navbar-toggler[b-p3l1yp9o1y] {
    background-color: rgba(255, 255, 255, 0.1);
}

.top-row[b-p3l1yp9o1y] {
    min-height: 3.5rem;
    background-color: rgba(0,0,0,0.4);
}

.navbar-brand[b-p3l1yp9o1y] {
    font-size: 1.1rem;
}

.bi[b-p3l1yp9o1y] {
    display: inline-block;
    position: relative;
    width: 1.25rem;
    height: 1.25rem;
    margin-right: 0.75rem;
    top: -1px;
    background-size: cover;
}

.bi-house-door-fill-nav-menu[b-p3l1yp9o1y] {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='white' class='bi bi-house-door-fill' viewBox='0 0 16 16'%3E%3Cpath d='M6.5 14.5v-3.505c0-.245.25-.495.5-.495h2c.25 0 .5.25.5.5v3.5a.5.5 0 0 0 .5.5h4a.5.5 0 0 0 .5-.5v-7a.5.5 0 0 0-.146-.354L13 5.793V2.5a.5.5 0 0 0-.5-.5h-1a.5.5 0 0 0-.5.5v1.293L8.354 1.146a.5.5 0 0 0-.708 0l-6 6A.5.5 0 0 0 1.5 7.5v7a.5.5 0 0 0 .5.5h4a.5.5 0 0 0 .5-.5Z'/%3E%3C/svg%3E");
}

.bi-plus-square-fill-nav-menu[b-p3l1yp9o1y] {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='white' class='bi bi-plus-square-fill' viewBox='0 0 16 16'%3E%3Cpath d='M2 0a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V2a2 2 0 0 0-2-2H2zm6.5 4.5v3h3a.5.5 0 0 1 0 1h-3v3a.5.5 0 0 1-1 0v-3h-3a.5.5 0 0 1 0-1h3v-3a.5.5 0 0 1 1 0z'/%3E%3C/svg%3E");
}

.bi-list-nested-nav-menu[b-p3l1yp9o1y] {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='white' class='bi bi-list-nested' viewBox='0 0 16 16'%3E%3Cpath fill-rule='evenodd' d='M4.5 11.5A.5.5 0 0 1 5 11h10a.5.5 0 0 1 0 1H5a.5.5 0 0 1-.5-.5zm-2-4A.5.5 0 0 1 3 7h10a.5.5 0 0 1 0 1H3a.5.5 0 0 1-.5-.5zm-2-4A.5.5 0 0 1 1 3h10a.5.5 0 0 1 0 1H1a.5.5 0 0 1-.5-.5z'/%3E%3C/svg%3E");
}

.nav-item[b-p3l1yp9o1y] {
    font-size: 0.9rem;
    padding-bottom: 0.5rem;
}

    .nav-item:first-of-type[b-p3l1yp9o1y] {
        padding-top: 1rem;
    }

    .nav-item:last-of-type[b-p3l1yp9o1y] {
        padding-bottom: 1rem;
    }

    .nav-item[b-p3l1yp9o1y]  a {
        color: #d7d7d7;
        border-radius: 4px;
        height: 3rem;
        display: flex;
        align-items: center;
        line-height: 3rem;
    }

.nav-item[b-p3l1yp9o1y]  a.active {
    background-color: rgba(255,255,255,0.37);
    color: white;
}

.nav-item[b-p3l1yp9o1y]  a:hover {
    background-color: rgba(255,255,255,0.1);
    color: white;
}

@media (min-width: 641px) {
    .navbar-toggler[b-p3l1yp9o1y] {
        display: none;
    }

    .collapse[b-p3l1yp9o1y] {
        /* Never collapse the sidebar for wide screens */
        display: block;
    }

    .nav-scrollable[b-p3l1yp9o1y] {
        /* Allow sidebar to scroll for tall menus */
        height: calc(100vh - 3.5rem);
        overflow-y: auto;
    }
}
