# Тестирование API

Этот документ содержит примеры для тестирования API системы управления товарами.

## Базовый URL
```
https://localhost:7297/api/products
```

## Примеры запросов

### 1. Получить все товары
```bash
# GET запрос
curl -k -X GET "https://localhost:7297/api/products"
```

**Ожидаемый ответ:**
```json
[
  {
    "id": 1,
    "name": "Ноутбук ASUS",
    "description": "Игровой ноутбук с высокой производительностью",
    "price": 85000.0,
    "category": "Компьютеры",
    "inStock": true,
    "createdDate": "2024-01-01T00:00:00Z"
  },
  {
    "id": 2,
    "name": "Смартфон Samsung Galaxy",
    "description": "Современный смартфон с отличной камерой",
    "price": 45000.0,
    "category": "Телефоны",
    "inStock": true,
    "createdDate": "2024-01-01T00:00:00Z"
  }
]
```

### 2. Получить товар по ID
```bash
# GET запрос для товара с ID = 1
curl -k -X GET "https://localhost:7297/api/products/1"
```

### 3. Создать новый товар
```bash
# POST запрос
curl -k -X POST "https://localhost:7297/api/products" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Планшет iPad",
    "description": "Планшет Apple с большим экраном",
    "price": 60000.0,
    "category": "Планшеты",
    "inStock": true
  }'
```

### 4. Обновить товар
```bash
# PUT запрос для обновления товара с ID = 1
curl -k -X PUT "https://localhost:7297/api/products/1" \
  -H "Content-Type: application/json" \
  -d '{
    "id": 1,
    "name": "Ноутбук ASUS ROG",
    "description": "Игровой ноутбук с улучшенной производительностью",
    "price": 95000.0,
    "category": "Компьютеры",
    "inStock": true,
    "createdDate": "2024-01-01T00:00:00Z"
  }'
```

### 5. Удалить товар
```bash
# DELETE запрос для удаления товара с ID = 3
curl -k -X DELETE "https://localhost:7297/api/products/3"
```

## Тестирование с помощью PowerShell

### Получить все товары
```powershell
$response = Invoke-RestMethod -Uri "https://localhost:7297/api/products" -Method GET -SkipCertificateCheck
$response | ConvertTo-Json -Depth 3
```

### Создать новый товар
```powershell
$newProduct = @{
    name = "Клавиатура механическая"
    description = "Игровая механическая клавиатура с RGB подсветкой"
    price = 8000.0
    category = "Периферия"
    inStock = $true
}

$json = $newProduct | ConvertTo-Json
$response = Invoke-RestMethod -Uri "https://localhost:7297/api/products" -Method POST -Body $json -ContentType "application/json" -SkipCertificateCheck
$response
```

## Тестирование валидации

### Невалидные данные - пустое название
```bash
curl -k -X POST "https://localhost:7297/api/products" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "",
    "description": "Товар без названия",
    "price": 1000.0,
    "category": "Тест",
    "inStock": true
  }'
```

**Ожидаемый ответ:** HTTP 400 Bad Request с ошибками валидации

### Невалидные данные - отрицательная цена
```bash
curl -k -X POST "https://localhost:7297/api/products" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Тестовый товар",
    "description": "Товар с отрицательной ценой",
    "price": -100.0,
    "category": "Тест",
    "inStock": true
  }'
```

### Невалидные данные - слишком длинное название
```bash
curl -k -X POST "https://localhost:7297/api/products" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Очень длинное название товара которое превышает максимально допустимую длину в сто символов и должно вызвать ошибку валидации",
    "description": "Товар с длинным названием",
    "price": 1000.0,
    "category": "Тест",
    "inStock": true
  }'
```

## Тестирование ошибок

### Товар не найден
```bash
# Попытка получить несуществующий товар
curl -k -X GET "https://localhost:7297/api/products/999"
```

**Ожидаемый ответ:** HTTP 404 Not Found

### Обновление несуществующего товара
```bash
curl -k -X PUT "https://localhost:7297/api/products/999" \
  -H "Content-Type: application/json" \
  -d '{
    "id": 999,
    "name": "Несуществующий товар",
    "description": "Этот товар не существует",
    "price": 1000.0,
    "category": "Тест",
    "inStock": true,
    "createdDate": "2024-01-01T00:00:00Z"
  }'
```

**Ожидаемый ответ:** HTTP 404 Not Found

## Коды ответов

| Код | Описание | Когда возникает |
|-----|----------|-----------------|
| 200 | OK | Успешное получение данных |
| 201 | Created | Успешное создание товара |
| 204 | No Content | Успешное обновление/удаление |
| 400 | Bad Request | Ошибки валидации |
| 404 | Not Found | Товар не найден |
| 500 | Internal Server Error | Внутренняя ошибка сервера |

## Автоматизированное тестирование

### Скрипт для полного тестирования (PowerShell)
```powershell
# Тест API системы управления товарами
$baseUrl = "https://localhost:7297/api/products"

Write-Host "Тестирование API..." -ForegroundColor Green

# 1. Получить все товары
Write-Host "1. Получение всех товаров..." -ForegroundColor Yellow
try {
    $products = Invoke-RestMethod -Uri $baseUrl -Method GET -SkipCertificateCheck
    Write-Host "✓ Получено товаров: $($products.Count)" -ForegroundColor Green
} catch {
    Write-Host "✗ Ошибка: $($_.Exception.Message)" -ForegroundColor Red
}

# 2. Создать новый товар
Write-Host "2. Создание нового товара..." -ForegroundColor Yellow
$newProduct = @{
    name = "Тестовый товар"
    description = "Товар для тестирования API"
    price = 1500.0
    category = "Тест"
    inStock = $true
} | ConvertTo-Json

try {
    $created = Invoke-RestMethod -Uri $baseUrl -Method POST -Body $newProduct -ContentType "application/json" -SkipCertificateCheck
    Write-Host "✓ Товар создан с ID: $($created.id)" -ForegroundColor Green
    $testId = $created.id
} catch {
    Write-Host "✗ Ошибка: $($_.Exception.Message)" -ForegroundColor Red
}

# 3. Получить созданный товар
if ($testId) {
    Write-Host "3. Получение созданного товара..." -ForegroundColor Yellow
    try {
        $product = Invoke-RestMethod -Uri "$baseUrl/$testId" -Method GET -SkipCertificateCheck
        Write-Host "✓ Товар получен: $($product.name)" -ForegroundColor Green
    } catch {
        Write-Host "✗ Ошибка: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# 4. Удалить тестовый товар
if ($testId) {
    Write-Host "4. Удаление тестового товара..." -ForegroundColor Yellow
    try {
        Invoke-RestMethod -Uri "$baseUrl/$testId" -Method DELETE -SkipCertificateCheck
        Write-Host "✓ Товар удален" -ForegroundColor Green
    } catch {
        Write-Host "✗ Ошибка: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host "Тестирование завершено!" -ForegroundColor Green
```

Сохраните этот скрипт как `test-api.ps1` и запустите:
```powershell
.\test-api.ps1
```
