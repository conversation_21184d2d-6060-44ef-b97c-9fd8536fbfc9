@page "/products"
@using BlazorClient.Models
@using BlazorClient.Services
@inject ProductService ProductService
@inject NavigationManager Navigation
@inject IJSRuntime JSRuntime

<PageTitle>Товары</PageTitle>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="/">Главная</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Товары</li>
                </ol>
            </nav>
        </div>
    </div>

    <div class="row mb-3">
        <div class="col-md-6">
            <h1>Управление товарами</h1>
        </div>
        <div class="col-md-6 text-end">
            <a href="/products/add" class="btn btn-primary">
                <i class="bi bi-plus-circle"></i> Добавить товар
            </a>
        </div>
    </div>

    <div class="row mb-3">
        <div class="col-md-6">
            <div class="input-group">
                <span class="input-group-text"><i class="bi bi-search"></i></span>
                <input type="text" class="form-control" placeholder="Поиск товаров..." @bind="searchTerm" @oninput="OnSearchChanged" />
            </div>
        </div>
        <div class="col-md-6">
            <select class="form-select" @onchange="OnCategoryChanged">
                <option value="">Все категории</option>
                @foreach (var category in categories)
                {
                    <option value="@category">@category</option>
                }
            </select>
        </div>
    </div>

    @if (isLoading)
    {
        <div class="text-center">
            <div class="spinner-border" role="status">
                <span class="visually-hidden">Загрузка...</span>
            </div>
        </div>
    }
    else if (filteredProducts.Any())
    {
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead class="table-dark">
                    <tr>
                        <th>ID</th>
                        <th>Название</th>
                        <th>Описание</th>
                        <th>Цена</th>
                        <th>Категория</th>
                        <th>В наличии</th>
                        <th>Дата создания</th>
                        <th>Действия</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach (var product in filteredProducts)
                    {
                        <tr>
                            <td>@product.Id</td>
                            <td>@product.Name</td>
                            <td>@(product.Description.Length > 50 ? product.Description.Substring(0, 50) + "..." : product.Description)</td>
                            <td>@product.Price.ToString("C", new System.Globalization.CultureInfo("ru-RU"))</td>
                            <td>@product.Category</td>
                            <td>
                                @if (product.InStock)
                                {
                                    <span class="badge bg-success">В наличии</span>
                                }
                                else
                                {
                                    <span class="badge bg-danger">Нет в наличии</span>
                                }
                            </td>
                            <td>@product.CreatedDate.ToString("dd.MM.yyyy")</td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="/products/edit/@product.Id" class="btn btn-sm btn-outline-primary" title="Редактировать">
                                        <i class="bi bi-pencil"></i>
                                    </a>
                                    <button class="btn btn-sm btn-outline-danger" @onclick="() => ShowDeleteModal(product)" title="Удалить">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    }
                </tbody>
            </table>
        </div>
    }
    else
    {
        <div class="alert alert-info text-center">
            <h4>Товары не найдены</h4>
            <p>Попробуйте изменить критерии поиска или <a href="/products/add">добавьте новый товар</a>.</p>
        </div>
    }
</div>

<!-- Modal для подтверждения удаления -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">Подтверждение удаления</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                @if (productToDelete != null)
                {
                    <p>Вы уверены, что хотите удалить товар?</p>
                    <div class="card">
                        <div class="card-body">
                            <h6 class="card-title">@productToDelete.Name</h6>
                            <p class="card-text">@productToDelete.Description</p>
                            <p class="card-text"><small class="text-muted">Цена: @productToDelete.Price.ToString("C", new System.Globalization.CultureInfo("ru-RU"))</small></p>
                        </div>
                    </div>
                }
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Отмена</button>
                <button type="button" class="btn btn-danger" @onclick="DeleteProduct">Удалить</button>
            </div>
        </div>
    </div>
</div>

@code {
    private List<Product> products = new();
    private List<Product> filteredProducts = new();
    private List<string> categories = new();
    private string searchTerm = string.Empty;
    private string selectedCategory = string.Empty;
    private bool isLoading = true;
    private Product? productToDelete;

    protected override async Task OnInitializedAsync()
    {
        await LoadProducts();
    }

    private async Task LoadProducts()
    {
        isLoading = true;
        products = await ProductService.GetProductsAsync();
        categories = products.Select(p => p.Category).Distinct().OrderBy(c => c).ToList();
        FilterProducts();
        isLoading = false;
    }

    private void FilterProducts()
    {
        filteredProducts = products.Where(p =>
            (string.IsNullOrEmpty(searchTerm) ||
             p.Name.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
             p.Description.Contains(searchTerm, StringComparison.OrdinalIgnoreCase)) &&
            (string.IsNullOrEmpty(selectedCategory) || p.Category == selectedCategory)
        ).ToList();
    }

    private void OnSearchChanged(ChangeEventArgs e)
    {
        searchTerm = e.Value?.ToString() ?? string.Empty;
        FilterProducts();
    }

    private void OnCategoryChanged(ChangeEventArgs e)
    {
        selectedCategory = e.Value?.ToString() ?? string.Empty;
        FilterProducts();
    }

    private async Task ShowDeleteModal(Product product)
    {
        productToDelete = product;
        await JSRuntime.InvokeVoidAsync("new bootstrap.Modal", "#deleteModal").AsTask();
        await JSRuntime.InvokeVoidAsync("document.getElementById('deleteModal').querySelector('.modal').classList.add('show')");
        await JSRuntime.InvokeVoidAsync("eval", "new bootstrap.Modal(document.getElementById('deleteModal')).show()");
    }

    private async Task DeleteProduct()
    {
        if (productToDelete != null)
        {
            var success = await ProductService.DeleteProductAsync(productToDelete.Id);
            if (success)
            {
                await JSRuntime.InvokeVoidAsync("eval", "bootstrap.Modal.getInstance(document.getElementById('deleteModal')).hide()");
                await LoadProducts();
            }
            else
            {
                // Здесь можно добавить уведомление об ошибке
                await JSRuntime.InvokeVoidAsync("alert", "Ошибка при удалении товара");
            }
        }
    }
}
