using System.Net.Http.Json;
using BlazorClient.Models;

namespace BlazorClient.Services;

public class ProductService
{
    private readonly HttpClient _httpClient;
    private readonly string _baseUrl = "api/products";

    public ProductService(HttpClient httpClient)
    {
        _httpClient = httpClient;
    }

    public async Task<List<Product>> GetProductsAsync()
    {
        try
        {
            var products = await _httpClient.GetFromJsonAsync<List<Product>>(_baseUrl);
            return products ?? new List<Product>();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Ошибка при получении товаров: {ex.Message}");
            return new List<Product>();
        }
    }

    public async Task<Product?> GetProductAsync(int id)
    {
        try
        {
            return await _httpClient.GetFromJsonAsync<Product>($"{_baseUrl}/{id}");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Ошибка при получении товара: {ex.Message}");
            return null;
        }
    }

    public async Task<bool> CreateProductAsync(Product product)
    {
        try
        {
            var response = await _httpClient.PostAsJsonAsync(_baseUrl, product);
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Ошибка при создании товара: {ex.Message}");
            return false;
        }
    }

    public async Task<bool> UpdateProductAsync(int id, Product product)
    {
        try
        {
            var response = await _httpClient.PutAsJsonAsync($"{_baseUrl}/{id}", product);
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Ошибка при обновлении товара: {ex.Message}");
            return false;
        }
    }

    public async Task<bool> DeleteProductAsync(int id)
    {
        try
        {
            var response = await _httpClient.DeleteAsync($"{_baseUrl}/{id}");
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Ошибка при удалении товара: {ex.Message}");
            return false;
        }
    }
}
