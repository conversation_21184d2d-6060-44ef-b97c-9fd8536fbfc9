using Microsoft.JSInterop;

namespace BlazorClient.Services;

public class ThemeService
{
    private readonly IJSRuntime _jsRuntime;
    private bool _isDarkTheme = false;

    public event Action? OnThemeChanged;

    public ThemeService(IJSRuntime jsRuntime)
    {
        _jsRuntime = jsRuntime;
    }

    public bool IsDarkTheme => _isDarkTheme;

    public async Task InitializeAsync()
    {
        try
        {
            var savedTheme = await _jsRuntime.InvokeAsync<string>("localStorage.getItem", "theme");
            _isDarkTheme = savedTheme == "dark";
            await ApplyThemeAsync();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Ошибка при инициализации темы: {ex.Message}");
        }
    }

    public async Task ToggleThemeAsync()
    {
        _isDarkTheme = !_isDarkTheme;
        await SaveThemeAsync();
        await ApplyThemeAsync();
        OnThemeChanged?.Invoke();
    }

    private async Task SaveThemeAsync()
    {
        try
        {
            var theme = _isDarkTheme ? "dark" : "light";
            await _jsRuntime.InvokeVoidAsync("localStorage.setItem", "theme", theme);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Ошибка при сохранении темы: {ex.Message}");
        }
    }

    private async Task ApplyThemeAsync()
    {
        try
        {
            var theme = _isDarkTheme ? "dark" : "light";
            await _jsRuntime.InvokeVoidAsync("document.documentElement.setAttribute", "data-bs-theme", theme);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Ошибка при применении темы: {ex.Message}");
        }
    }
}
