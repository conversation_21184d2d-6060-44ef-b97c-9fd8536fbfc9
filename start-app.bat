@echo off
chcp 65001 > nul
echo Starting Product Management System...
echo.

echo Building projects...
dotnet build BlazorServer
if %errorlevel% neq 0 (
    echo Error building server project
    pause
    exit /b 1
)

dotnet build BlazorClient
if %errorlevel% neq 0 (
    echo Error building client project
    pause
    exit /b 1
)

echo.
echo Starting server...
start "BlazorServer" cmd /k "cd BlazorServer && dotnet run"

echo Waiting for server to start...
timeout /t 5 /nobreak > nul

echo.
echo Starting client...
start "BlazorClient" cmd /k "cd BlazorClient && dotnet run"

echo.
echo Application started!
echo Server: https://localhost:7297
echo Client: https://localhost:7084
echo.
echo Press any key to exit...
pause > nul
