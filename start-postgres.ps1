# Скрипт запуска PostgreSQL через Docker

Write-Host "🐳 Запуск PostgreSQL через Docker..." -ForegroundColor Green
Write-Host ""

# Проверка наличия Docker
Write-Host "🔍 Проверка Docker..." -ForegroundColor Yellow
try {
    $dockerVersion = docker --version 2>$null
    if ($dockerVersion) {
        Write-Host "✅ Docker найден: $dockerVersion" -ForegroundColor Green
    } else {
        throw "Docker не найден"
    }
} catch {
    Write-Host "❌ Docker не установлен" -ForegroundColor Red
    Write-Host "📥 Установите Docker Desktop: https://www.docker.com/products/docker-desktop" -ForegroundColor Cyan
    Read-Host "Нажмите Enter для выхода"
    exit 1
}

# Проверка запуска Docker
Write-Host "🔄 Проверка состояния Docker..." -ForegroundColor Yellow
try {
    docker info > $null 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Docker запущен" -ForegroundColor Green
    } else {
        throw "Docker не запущен"
    }
} catch {
    Write-Host "❌ Docker не запущен. Запустите Docker Desktop" -ForegroundColor Red
    Read-Host "Нажмите Enter для выхода"
    exit 1
}

# Остановка существующих контейнеров
Write-Host ""
Write-Host "🛑 Остановка существующих контейнеров..." -ForegroundColor Yellow
docker-compose down 2>$null

# Запуск PostgreSQL
Write-Host ""
Write-Host "🚀 Запуск PostgreSQL и pgAdmin..." -ForegroundColor Green
docker-compose up -d

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Контейнеры запущены успешно" -ForegroundColor Green
    
    Write-Host ""
    Write-Host "⏳ Ожидание готовности PostgreSQL..." -ForegroundColor Yellow
    
    # Ждем готовности PostgreSQL
    $maxAttempts = 30
    $attempt = 0
    
    do {
        $attempt++
        Start-Sleep -Seconds 2
        
        try {
            $result = docker exec product-management-db pg_isready -U postgres 2>$null
            if ($LASTEXITCODE -eq 0) {
                Write-Host "✅ PostgreSQL готов к работе!" -ForegroundColor Green
                break
            }
        } catch {
            # Продолжаем ожидание
        }
        
        Write-Host "⏳ Попытка $attempt из $maxAttempts..." -ForegroundColor Gray
        
    } while ($attempt -lt $maxAttempts)
    
    if ($attempt -ge $maxAttempts) {
        Write-Host "❌ PostgreSQL не готов после $maxAttempts попыток" -ForegroundColor Red
    }
    
} else {
    Write-Host "❌ Ошибка при запуске контейнеров" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "🎉 PostgreSQL запущен!" -ForegroundColor Green
Write-Host ""
Write-Host "📋 Информация о подключении:" -ForegroundColor Cyan
Write-Host "   🐘 PostgreSQL:" -ForegroundColor White
Write-Host "      Хост: localhost" -ForegroundColor Gray
Write-Host "      Порт: 5432" -ForegroundColor Gray
Write-Host "      База данных: ProductManagement" -ForegroundColor Gray
Write-Host "      Пользователь: postgres" -ForegroundColor Gray
Write-Host "      Пароль: postgres" -ForegroundColor Gray
Write-Host ""
Write-Host "   🌐 pgAdmin (веб-интерфейс):" -ForegroundColor White
Write-Host "      URL: http://localhost:8080" -ForegroundColor Gray
Write-Host "      Email: <EMAIL>" -ForegroundColor Gray
Write-Host "      Пароль: admin" -ForegroundColor Gray
Write-Host ""

# Создание миграций
Write-Host "🔄 Создание и применение миграций..." -ForegroundColor Yellow
try {
    Set-Location "BlazorServer"
    
    # Удаляем старые миграции
    if (Test-Path "Migrations") {
        Remove-Item -Recurse -Force "Migrations"
        Write-Host "🗑️ Старые миграции удалены" -ForegroundColor Yellow
    }
    
    # Создаем миграцию
    Write-Host "📝 Создание миграции..." -ForegroundColor Yellow
    dotnet ef migrations add InitialCreate
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Миграция создана" -ForegroundColor Green
        
        # Применяем миграцию
        Write-Host "⚡ Применение миграции..." -ForegroundColor Yellow
        dotnet ef database update
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ База данных настроена" -ForegroundColor Green
        } else {
            Write-Host "❌ Ошибка при применении миграции" -ForegroundColor Red
        }
    } else {
        Write-Host "❌ Ошибка при создании миграции" -ForegroundColor Red
    }
    
    Set-Location ".."
} catch {
    Write-Host "❌ Ошибка: $($_.Exception.Message)" -ForegroundColor Red
    Set-Location ".."
}

Write-Host ""
Write-Host "🚀 Готово! Теперь можно запускать приложение:" -ForegroundColor Green
Write-Host "   .\start-app.ps1" -ForegroundColor White
Write-Host ""
Write-Host "🛑 Для остановки PostgreSQL:" -ForegroundColor Red
Write-Host "   docker-compose down" -ForegroundColor White
Write-Host ""

Read-Host "Нажмите Enter для завершения"
