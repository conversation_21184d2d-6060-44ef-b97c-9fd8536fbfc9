﻿<div class="top-row ps-3 navbar navbar-dark">
    <div class="container-fluid">
        <a class="navbar-brand" href="">Управление товарами</a>
        <div class="d-flex align-items-center">
            <button class="btn btn-outline-light me-2" @onclick="ToggleTheme" title="Переключить тему">
                @if (themeService.IsDarkTheme)
                {
                    <span class="bi bi-sun"></span>
                }
                else
                {
                    <span class="bi bi-moon"></span>
                }
            </button>
            <button title="Navigation menu" class="navbar-toggler" @onclick="ToggleNavMenu">
                <span class="navbar-toggler-icon"></span>
            </button>
        </div>
    </div>
</div>

<div class="@NavMenuCssClass nav-scrollable" @onclick="ToggleNavMenu">
    <nav class="nav flex-column">
        <div class="nav-item px-3">
            <NavLink class="nav-link" href="" Match="NavLinkMatch.All">
                <span class="bi bi-house-door-fill-nav-menu" aria-hidden="true"></span> Главная
            </NavLink>
        </div>
        <div class="nav-item px-3">
            <NavLink class="nav-link" href="products">
                <span class="bi bi-box-seam-nav-menu" aria-hidden="true"></span> Товары
            </NavLink>
        </div>
        <div class="nav-item px-3">
            <NavLink class="nav-link" href="products/add">
                <span class="bi bi-plus-square-fill-nav-menu" aria-hidden="true"></span> Добавить товар
            </NavLink>
        </div>
    </nav>
</div>

@using BlazorClient.Services
@inject ThemeService themeService

@code {
    private bool collapseNavMenu = true;

    private string? NavMenuCssClass => collapseNavMenu ? "collapse" : null;

    private void ToggleNavMenu()
    {
        collapseNavMenu = !collapseNavMenu;
    }

    private async Task ToggleTheme()
    {
        await themeService.ToggleThemeAsync();
        StateHasChanged();
    }

    protected override void OnInitialized()
    {
        themeService.OnThemeChanged += StateHasChanged;
    }

    public void Dispose()
    {
        themeService.OnThemeChanged -= StateHasChanged;
    }
}
