{"format": 1, "restore": {"C:\\study\\corporate systems\\4 semestr\\task 6\\BlazorServer\\BlazorServer.csproj": {}}, "projects": {"C:\\study\\corporate systems\\4 semestr\\task 6\\BlazorServer\\BlazorServer.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\study\\corporate systems\\4 semestr\\task 6\\BlazorServer\\BlazorServer.csproj", "projectName": "BlazorServer", "projectPath": "C:\\study\\corporate systems\\4 semestr\\task 6\\BlazorServer\\BlazorServer.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\study\\corporate systems\\4 semestr\\task 6\\BlazorServer\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.AspNetCore.OpenApi": {"target": "Package", "version": "[9.0.2, )"}, "Microsoft.EntityFrameworkCore.Design": {"target": "Package", "version": "[9.0.2, )"}, "Microsoft.EntityFrameworkCore.Tools": {"target": "Package", "version": "[9.0.2, )"}, "Npgsql.EntityFrameworkCore.PostgreSQL": {"target": "Package", "version": "[9.0.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.200/PortableRuntimeIdentifierGraph.json"}}}}}