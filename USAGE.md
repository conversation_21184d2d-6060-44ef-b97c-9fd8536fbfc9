# Руководство пользователя - Система управления товарами

## Быстрый старт

### 1. Запуск приложения

**Автоматический запуск (рекомендуется):**
```bash
# Запустите файл start-app.bat
start-app.bat
```

**Ручной запуск:**
```bash
# Терминал 1 - Сервер
cd BlazorServer
dotnet run

# Терминал 2 - Клиент  
cd BlazorClient
dotnet run
```

### 2. Доступ к приложению
- **Клиентское приложение**: https://localhost:7084
- **API сервера**: https://localhost:7297

## Основные функции

### Главная страница
- Обзор системы и быстрый доступ к основным функциям
- Карточки с описанием возможностей
- Навигация к списку товаров

### Управление товарами

#### Просмотр списка товаров (`/products`)
- **Таблица товаров** с полной информацией:
  - ID, название, описание, цена, категория
  - Статус наличия на складе
  - Дата создания
- **Поиск товаров** по названию и описанию
- **Фильтрация по категориям** через выпадающий список
- **Действия с товарами**:
  - Кнопка редактирования (синяя иконка карандаша)
  - Кнопка удаления (красная иконка корзины)

#### Добавление нового товара (`/products/add`)
- **Форма создания** с полями:
  - Название товара (обязательное, до 100 символов)
  - Описание (до 500 символов)
  - Цена (обязательное, больше 0)
  - Категория (обязательное, до 50 символов)
  - Чекбокс "Товар в наличии"
- **Валидация** на клиентской стороне
- **Индикатор загрузки** при сохранении
- **Обработка ошибок** с отображением сообщений

#### Редактирование товара (`/products/edit/{id}`)
- **Предзаполненная форма** с текущими данными товара
- **Все поля доступны для редактирования** кроме ID и даты создания
- **Сохранение изменений** с валидацией
- **Отображение даты создания** (только для чтения)

#### Удаление товара
- **Модальное окно подтверждения** с информацией о товаре
- **Предварительный просмотр** удаляемого товара
- **Безопасное удаление** с подтверждением

### Дополнительные возможности

#### Переключение тем
- **Кнопка в навигации** (иконка солнца/луны)
- **Светлая и темная темы** Bootstrap
- **Сохранение выбора** в localStorage браузера
- **Автоматическое применение** при следующем посещении

#### Навигация
- **Breadcrumb навигация** на всех страницах
- **Активные ссылки** в главном меню
- **Responsive дизайн** для мобильных устройств

## Горячие клавиши и советы

### Навигация
- **Главная**: Логотип или breadcrumb "Главная"
- **Список товаров**: Меню "Товары" или кнопка "Перейти к товарам"
- **Добавить товар**: Меню "Добавить товар" или кнопка "+" в списке

### Поиск и фильтрация
- **Поиск**: Введите текст в поле поиска для фильтрации по названию/описанию
- **Категории**: Выберите категорию из выпадающего списка
- **Сброс фильтров**: Очистите поле поиска и выберите "Все категории"

### Работа с формами
- **Обязательные поля** отмечены красной звездочкой (*)
- **Валидация** происходит в реальном времени
- **Ошибки** отображаются под соответствующими полями
- **Кнопка "Отмена"** возвращает к списку товаров без сохранения

## Устранение неполадок

### Приложение не запускается
1. Убедитесь, что установлен .NET 9.0 или новее
2. Проверьте, что порты 7297 и 7084 свободны
3. Запустите `dotnet restore` в корневой папке

### Ошибки подключения к API
1. Убедитесь, что сервер запущен (BlazorServer)
2. Проверьте URL API в Program.cs клиента
3. Проверьте настройки CORS в серверной части

### Проблемы с базой данных
1. Удалите файл `products.db` для пересоздания БД
2. Перезапустите серверное приложение
3. Проверьте права доступа к папке проекта

### Проблемы с темами
1. Очистите localStorage браузера
2. Обновите страницу (F5)
3. Проверьте подключение Bootstrap CSS

## API Reference

### Endpoints
- `GET /api/products` - Получить все товары
- `GET /api/products/{id}` - Получить товар по ID
- `POST /api/products` - Создать новый товар
- `PUT /api/products/{id}` - Обновить товар
- `DELETE /api/products/{id}` - Удалить товар

### Модель Product
```json
{
  "id": 1,
  "name": "Название товара",
  "description": "Описание товара",
  "price": 1000.00,
  "category": "Категория",
  "inStock": true,
  "createdDate": "2024-01-01T00:00:00Z"
}
```

## Техническая поддержка

При возникновении проблем:
1. Проверьте консоль браузера на наличие ошибок JavaScript
2. Проверьте логи сервера в терминале
3. Убедитесь в корректности данных при создании/редактировании товаров
4. Перезапустите приложение при необходимости
