# Скрипт запуска системы управления товарами
Write-Host "🚀 Запуск системы управления товарами..." -ForegroundColor Green
Write-Host ""

# Проверка наличия .NET
try {
    $dotnetVersion = dotnet --version
    Write-Host "✅ .NET версия: $dotnetVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ .NET не найден. Установите .NET 8.0 или новее" -ForegroundColor Red
    Read-Host "Нажмите Enter для выхода"
    exit 1
}

Write-Host ""
Write-Host "🔨 Сборка проектов..." -ForegroundColor Yellow

# Сборка серверного проекта
Write-Host "Сборка BlazorServer..." -ForegroundColor Cyan
$serverBuild = dotnet build BlazorServer --verbosity quiet
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Ошибка при сборке серверного проекта" -ForegroundColor Red
    Read-Host "Нажмите Enter для выхода"
    exit 1
}
Write-Host "✅ BlazorServer собран успешно" -ForegroundColor Green

# Сборка клиентского проекта
Write-Host "Сборка BlazorClient..." -ForegroundColor Cyan
$clientBuild = dotnet build BlazorClient --verbosity quiet
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Ошибка при сборке клиентского проекта" -ForegroundColor Red
    Read-Host "Нажмите Enter для выхода"
    exit 1
}
Write-Host "✅ BlazorClient собран успешно" -ForegroundColor Green

Write-Host ""
Write-Host "🌐 Запуск приложений..." -ForegroundColor Yellow

# Запуск сервера в новом окне
Write-Host "Запуск сервера..." -ForegroundColor Cyan
$serverProcess = Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd '$PWD\BlazorServer'; Write-Host '🔧 Запуск BlazorServer...' -ForegroundColor Green; dotnet run" -PassThru
Start-Sleep -Seconds 2

# Запуск клиента в новом окне
Write-Host "Запуск клиента..." -ForegroundColor Cyan
$clientProcess = Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd '$PWD\BlazorClient'; Write-Host '🌐 Запуск BlazorClient...' -ForegroundColor Green; dotnet run" -PassThru

Write-Host ""
Write-Host "⏳ Ожидание запуска серверов..." -ForegroundColor Yellow
Start-Sleep -Seconds 8

Write-Host ""
Write-Host "🎉 Приложение запущено!" -ForegroundColor Green
Write-Host ""
Write-Host "📍 Адреса приложений:" -ForegroundColor Cyan
Write-Host "   🔧 API Сервер:  https://localhost:7297" -ForegroundColor White
Write-Host "   🌐 Веб-клиент:  https://localhost:7084" -ForegroundColor White
Write-Host ""
Write-Host "💡 Советы:" -ForegroundColor Yellow
Write-Host "   • Откройте https://localhost:7084 в браузере" -ForegroundColor Gray
Write-Host "   • Попробуйте переключить тему (кнопка в правом верхнем углу)" -ForegroundColor Gray
Write-Host "   • Добавьте новый товар или отредактируйте существующий" -ForegroundColor Gray
Write-Host ""

# Попытка открыть браузер
try {
    Write-Host "🌐 Открытие браузера..." -ForegroundColor Cyan
    Start-Process "https://localhost:7084"
    Write-Host "✅ Браузер открыт" -ForegroundColor Green
} catch {
    Write-Host "⚠️  Не удалось автоматически открыть браузер" -ForegroundColor Yellow
    Write-Host "   Откройте вручную: https://localhost:7084" -ForegroundColor Gray
}

Write-Host ""
Write-Host "🛑 Для остановки приложений закройте окна PowerShell или нажмите Ctrl+C в каждом окне" -ForegroundColor Red
Write-Host ""
Read-Host "Нажмите Enter для завершения этого скрипта"
