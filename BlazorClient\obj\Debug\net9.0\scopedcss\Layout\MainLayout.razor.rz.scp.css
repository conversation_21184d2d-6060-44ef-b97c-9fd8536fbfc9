.page[b-aa2zzlyc8p] {
    position: relative;
    display: flex;
    flex-direction: column;
}

main[b-aa2zzlyc8p] {
    flex: 1;
}

.sidebar[b-aa2zzlyc8p] {
    background-image: linear-gradient(180deg, rgb(5, 39, 103) 0%, #3a0647 70%);
}

.top-row[b-aa2zzlyc8p] {
    background-color: #f7f7f7;
    border-bottom: 1px solid #d6d5d5;
    justify-content: flex-end;
    height: 3.5rem;
    display: flex;
    align-items: center;
}

    .top-row[b-aa2zzlyc8p]  a, .top-row[b-aa2zzlyc8p]  .btn-link {
        white-space: nowrap;
        margin-left: 1.5rem;
        text-decoration: none;
    }

    .top-row[b-aa2zzlyc8p]  a:hover, .top-row[b-aa2zzlyc8p]  .btn-link:hover {
        text-decoration: underline;
    }

    .top-row[b-aa2zzlyc8p]  a:first-child {
        overflow: hidden;
        text-overflow: ellipsis;
    }

@media (max-width: 640.98px) {
    .top-row[b-aa2zzlyc8p] {
        justify-content: space-between;
    }

    .top-row[b-aa2zzlyc8p]  a, .top-row[b-aa2zzlyc8p]  .btn-link {
        margin-left: 0;
    }
}

@media (min-width: 641px) {
    .page[b-aa2zzlyc8p] {
        flex-direction: row;
    }

    .sidebar[b-aa2zzlyc8p] {
        width: 250px;
        height: 100vh;
        position: sticky;
        top: 0;
    }

    .top-row[b-aa2zzlyc8p] {
        position: sticky;
        top: 0;
        z-index: 1;
    }

    .top-row.auth[b-aa2zzlyc8p]  a:first-child {
        flex: 1;
        text-align: right;
        width: 0;
    }

    .top-row[b-aa2zzlyc8p], article[b-aa2zzlyc8p] {
        padding-left: 2rem !important;
        padding-right: 1.5rem !important;
    }
}
