@page "/products/edit/{id:int}"
@using BlazorClient.Models
@using BlazorClient.Services
@using System.ComponentModel.DataAnnotations
@inject ProductService ProductService
@inject NavigationManager Navigation

<PageTitle>Редактировать товар</PageTitle>

<div class="container">
    <div class="row">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="/">Главная</a></li>
                    <li class="breadcrumb-item"><a href="/products">Товары</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Редактировать товар</li>
                </ol>
            </nav>
        </div>
    </div>

    @if (isLoading)
    {
        <div class="text-center">
            <div class="spinner-border" role="status">
                <span class="visually-hidden">Загрузка...</span>
            </div>
        </div>
    }
    else if (product == null)
    {
        <div class="alert alert-danger" role="alert">
            <h4 class="alert-heading">Товар не найден</h4>
            <p>Товар с указанным ID не существует.</p>
            <hr>
            <a href="/products" class="btn btn-primary">Вернуться к списку товаров</a>
        </div>
    }
    else
    {
        <div class="row">
            <div class="col-md-8 offset-md-2">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title mb-0">Редактировать товар #@Id</h3>
                    </div>
                    <div class="card-body">
                        <EditForm Model="product" OnValidSubmit="HandleValidSubmit">
                            <DataAnnotationsValidator />
                            
                            <div class="mb-3">
                                <label for="name" class="form-label">Название товара <span class="text-danger">*</span></label>
                                <InputText id="name" class="form-control" @bind-Value="product.Name" placeholder="Введите название товара" />
                                <ValidationMessage For="@(() => product.Name)" class="text-danger" />
                            </div>

                            <div class="mb-3">
                                <label for="description" class="form-label">Описание</label>
                                <InputTextArea id="description" class="form-control" @bind-Value="product.Description" 
                                             placeholder="Введите описание товара" rows="3" />
                                <ValidationMessage For="@(() => product.Description)" class="text-danger" />
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="price" class="form-label">Цена <span class="text-danger">*</span></label>
                                        <div class="input-group">
                                            <InputNumber id="price" class="form-control" @bind-Value="product.Price" 
                                                       placeholder="0.00" step="0.01" />
                                            <span class="input-group-text">₽</span>
                                        </div>
                                        <ValidationMessage For="@(() => product.Price)" class="text-danger" />
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="category" class="form-label">Категория <span class="text-danger">*</span></label>
                                        <InputText id="category" class="form-control" @bind-Value="product.Category" 
                                                 placeholder="Введите категорию" />
                                        <ValidationMessage For="@(() => product.Category)" class="text-danger" />
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <div class="form-check">
                                    <InputCheckbox id="inStock" class="form-check-input" @bind-Value="product.InStock" />
                                    <label class="form-check-label" for="inStock">
                                        Товар в наличии
                                    </label>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">Дата создания</label>
                                <input type="text" class="form-control" value="@product.CreatedDate.ToString("dd.MM.yyyy HH:mm")" readonly />
                            </div>

                            @if (isSubmitting)
                            {
                                <div class="d-flex justify-content-center mb-3">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">Сохранение...</span>
                                    </div>
                                </div>
                            }

                            @if (!string.IsNullOrEmpty(errorMessage))
                            {
                                <div class="alert alert-danger" role="alert">
                                    @errorMessage
                                </div>
                            }

                            <div class="d-flex justify-content-between">
                                <button type="button" class="btn btn-secondary" @onclick="Cancel">
                                    <i class="bi bi-arrow-left"></i> Отмена
                                </button>
                                <button type="submit" class="btn btn-primary" disabled="@isSubmitting">
                                    @if (isSubmitting)
                                    {
                                        <span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                                    }
                                    <i class="bi bi-check-circle"></i> Сохранить изменения
                                </button>
                            </div>
                        </EditForm>
                    </div>
                </div>
            </div>
        </div>
    }
</div>

@code {
    [Parameter] public int Id { get; set; }

    private Product? product;
    private bool isLoading = true;
    private bool isSubmitting = false;
    private string errorMessage = string.Empty;

    protected override async Task OnInitializedAsync()
    {
        await LoadProduct();
    }

    private async Task LoadProduct()
    {
        isLoading = true;
        product = await ProductService.GetProductAsync(Id);
        isLoading = false;
    }

    private async Task HandleValidSubmit()
    {
        if (product == null) return;

        isSubmitting = true;
        errorMessage = string.Empty;

        try
        {
            var success = await ProductService.UpdateProductAsync(Id, product);
            if (success)
            {
                Navigation.NavigateTo("/products");
            }
            else
            {
                errorMessage = "Произошла ошибка при сохранении изменений. Попробуйте еще раз.";
            }
        }
        catch (Exception ex)
        {
            errorMessage = $"Произошла ошибка: {ex.Message}";
        }
        finally
        {
            isSubmitting = false;
        }
    }

    private void Cancel()
    {
        Navigation.NavigateTo("/products");
    }
}
