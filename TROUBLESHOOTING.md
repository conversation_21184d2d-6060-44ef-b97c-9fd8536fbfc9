# 🔧 Устранение неполадок

## 🚨 Проблемы с запуском

### Проблема: Ошиб<PERSON>и кодировки в bat файлах
**Симптомы:** Сообщения типа "'�оекта' is not recognized as an internal or external command"

**Решение:**
1. Используйте PowerShell скрипт вместо bat файла:
   ```powershell
   .\start-app.ps1
   ```

2. Или запускайте проекты по отдельности:
   ```cmd
   # В первом терминале:
   start-server.bat

   # Во втором терминале:
   start-client.bat
   ```

3. Или вручную:
   ```cmd
   # Терминал 1:
   cd BlazorServer
   dotnet run

   # Терминал 2:
   cd BlazorClient
   dotnet run
   ```

### Проблема: "dotnet command not found"
**Решение:**
1. Установите .NET 8.0 или новее с официального сайта Microsoft
2. Перезапустите командную строку
3. Проверьте установку: `dotnet --version`

### Проблема: Порты заняты
**Симптомы:** Ошибки типа "Address already in use"

**Решение:**
1. Найдите процессы, использующие порты:
   ```cmd
   netstat -ano | findstr :7297
   netstat -ano | findstr :7084
   ```

2. Завершите процессы:
   ```cmd
   taskkill /PID [номер_процесса] /F
   ```

3. Или измените порты в файлах launchSettings.json

### Проблема: Ошибки сборки
**Решение:**
1. Очистите проекты:
   ```cmd
   dotnet clean
   dotnet restore
   dotnet build
   ```

2. Удалите папки bin и obj:
   ```cmd
   rmdir /s BlazorServer\bin
   rmdir /s BlazorServer\obj
   rmdir /s BlazorClient\bin
   rmdir /s BlazorClient\obj
   ```

## 🌐 Проблемы с браузером

### Проблема: "This site can't be reached"
**Решение:**
1. Убедитесь, что сервер запущен (должно быть сообщение "Now listening on")
2. Попробуйте HTTP вместо HTTPS: http://localhost:5072
3. Проверьте настройки брандмауэра

### Проблема: Ошибки CORS
**Симптомы:** Ошибки в консоли браузера о CORS

**Решение:**
1. Убедитесь, что сервер запущен первым
2. Проверьте настройки CORS в Program.cs сервера
3. Используйте правильные порты клиента в настройках CORS

### Проблема: Белый экран в браузере
**Решение:**
1. Откройте консоль разработчика (F12)
2. Проверьте ошибки в консоли
3. Убедитесь, что клиент полностью загрузился
4. Попробуйте обновить страницу (Ctrl+F5)

## 🐘 Проблемы с PostgreSQL

### Проблема: "Connection refused" или "server doesn't exist"
**Решение:**
1. **Проверьте, запущен ли PostgreSQL:**
   ```cmd
   # Для Docker:
   docker ps | findstr postgres

   # Для локальной установки:
   Get-Service postgresql*
   ```

2. **Запустите PostgreSQL:**
   ```cmd
   # Через Docker:
   .\start-postgres.ps1

   # Локальная служба:
   Start-Service postgresql-x64-*
   ```

### Проблема: "password authentication failed"
**Решение:**
1. Проверьте настройки в appsettings.json
2. Для Docker убедитесь, что пароль `postgres`
3. Для локальной установки сбросьте пароль:
   ```cmd
   psql -U postgres -c "ALTER USER postgres PASSWORD 'postgres';"
   ```

### Проблема: Ошибки миграций Entity Framework
**Решение:**
1. Удалите папку миграций:
   ```cmd
   rmdir /s BlazorServer\Migrations
   ```

2. Пересоздайте миграции:
   ```cmd
   cd BlazorServer
   dotnet ef migrations add InitialCreate
   dotnet ef database update
   ```

3. Если база данных повреждена:
   ```cmd
   # Удалите базу данных
   psql -U postgres -c "DROP DATABASE IF EXISTS \"ProductManagement\";"

   # Создайте заново
   psql -U postgres -c "CREATE DATABASE \"ProductManagement\";"
   ```

### Проблема: Нет тестовых данных
**Решение:**
1. Проверьте метод OnModelCreating в ApplicationDbContext
2. Пересоздайте базу данных:
   ```cmd
   dotnet ef database drop
   dotnet ef database update
   ```

### Проблема: Порт 5432 занят
**Решение:**
1. Найдите процесс:
   ```cmd
   netstat -ano | findstr :5432
   ```

2. Остановите конфликтующий процесс или измените порт в docker-compose.yml

## 🔧 Общие советы

### Проверка статуса приложений
```cmd
# Проверка процессов .NET
tasklist | findstr dotnet

# Проверка портов
netstat -ano | findstr :7297
netstat -ano | findstr :7084
```

### Полная перезагрузка
```cmd
# 1. Остановите все процессы dotnet
taskkill /im dotnet.exe /f

# 2. Очистите проекты
dotnet clean

# 3. Восстановите пакеты
dotnet restore

# 4. Пересоберите
dotnet build

# 5. Запустите заново
```

### Логи и отладка
1. **Серверные логи** отображаются в консоли сервера
2. **Клиентские ошибки** видны в консоли браузера (F12)
3. **Сетевые запросы** можно отслеживать во вкладке Network

## 📞 Если ничего не помогает

1. **Проверьте требования:**
   - Windows 10/11
   - .NET 8.0 или новее
   - Свободные порты 7297 и 7084

2. **Попробуйте минимальный запуск:**
   ```cmd
   cd BlazorServer
   dotnet run --urls "http://localhost:5000"
   ```

3. **Создайте новый проект** и сравните с рабочим

4. **Проверьте файлы проекта** на наличие ошибок в csproj файлах

## 🆘 Экстренные команды

### Принудительная остановка всех процессов
```cmd
taskkill /im dotnet.exe /f
taskkill /im "Product Management Server" /f
taskkill /im "Product Management Client" /f
```

### Сброс к заводским настройкам
```cmd
# Удалить все временные файлы
rmdir /s /q BlazorServer\bin
rmdir /s /q BlazorServer\obj
rmdir /s /q BlazorClient\bin
rmdir /s /q BlazorClient\obj
del BlazorServer\products.db*

# Восстановить проект
dotnet restore
dotnet build
```

### Альтернативные порты
Если стандартные порты не работают, измените в файлах:
- `BlazorServer/Properties/launchSettings.json`
- `BlazorClient/Program.cs` (BaseAddress)
- `BlazorServer/Program.cs` (CORS настройки)
