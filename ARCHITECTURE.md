# Архитектура системы управления товарами

## Обзор архитектуры

Система построена по принципу клиент-серверной архитектуры с четким разделением ответственности:

```
┌─────────────────┐    HTTP/HTTPS    ┌─────────────────┐
│   BlazorClient  │ ◄──────────────► │   BlazorServer  │
│ (WebAssembly)   │      API         │  (ASP.NET Core) │
└─────────────────┘                  └─────────────────┘
                                              │
                                              ▼
                                     ┌─────────────────┐
                                     │   SQLite DB     │
                                     │   (products.db) │
                                     └─────────────────┘
```

## Серверная часть (BlazorServer)

### Технологический стек
- **ASP.NET Core 9.0** - веб-фреймворк
- **Entity Framework Core** - ORM для работы с базой данных
- **SQLite** - база данных
- **Data Annotations** - валидация моделей

### Структура проекта
```
BlazorServer/
├── Controllers/
│   └── ProductsController.cs      # REST API контроллер
├── Data/
│   └── ApplicationDbContext.cs    # Контекст базы данных
├── Models/
│   └── Product.cs                 # Модель данных
├── Properties/
│   └── launchSettings.json        # Настройки запуска
├── appsettings.json               # Конфигурация приложения
└── Program.cs                     # Точка входа и конфигурация
```

### Компоненты

#### 1. Модель данных (Product)
```csharp
public class Product
{
    public int Id { get; set; }                    // Первичный ключ
    public string Name { get; set; }               // Название (обязательное, до 100 символов)
    public string Description { get; set; }        // Описание (до 500 символов)
    public decimal Price { get; set; }             // Цена (обязательное, > 0)
    public string Category { get; set; }           // Категория (обязательное, до 50 символов)
    public bool InStock { get; set; }              // Наличие на складе
    public DateTime CreatedDate { get; set; }      // Дата создания
}
```

#### 2. Контекст базы данных (ApplicationDbContext)
- Наследуется от `DbContext`
- Содержит `DbSet<Product> Products`
- Настройка модели через Fluent API
- Инициализация тестовых данных

#### 3. API контроллер (ProductsController)
- **GET /api/products** - получить все товары
- **GET /api/products/{id}** - получить товар по ID
- **POST /api/products** - создать новый товар
- **PUT /api/products/{id}** - обновить товар
- **DELETE /api/products/{id}** - удалить товар

### Особенности реализации

#### Валидация
- Использование Data Annotations на модели
- Проверка ModelState в контроллере
- Возврат детальных ошибок валидации

#### Обработка ошибок
- Try-catch блоки во всех методах контроллера
- Логирование ошибок через ILogger
- Возврат соответствующих HTTP статус-кодов

#### CORS
- Настройка для работы с Blazor WebAssembly клиентом
- Разрешение запросов с клиентских портов
- Поддержка всех HTTP методов и заголовков

## Клиентская часть (BlazorClient)

### Технологический стек
- **Blazor WebAssembly** - SPA фреймворк
- **Bootstrap 5** - CSS фреймворк
- **Bootstrap Icons** - иконки
- **System.Net.Http.Json** - HTTP клиент

### Структура проекта
```
BlazorClient/
├── Components/
│   ├── Alert.razor                # Компонент уведомлений
│   └── LoadingSpinner.razor       # Компонент загрузки
├── Layout/
│   ├── MainLayout.razor           # Основной макет
│   └── NavMenu.razor              # Навигационное меню
├── Models/
│   └── Product.cs                 # DTO модель
├── Pages/
│   ├── Home.razor                 # Главная страница
│   ├── Products.razor             # Список товаров
│   ├── AddProduct.razor           # Добавление товара
│   └── EditProduct.razor          # Редактирование товара
├── Services/
│   ├── ProductService.cs          # HTTP сервис для API
│   └── ThemeService.cs            # Сервис управления темами
├── wwwroot/
│   ├── index.html                 # Главная HTML страница
│   └── css/                       # Стили
├── App.razor                      # Корневой компонент
├── Program.cs                     # Точка входа
└── _Imports.razor                 # Глобальные using директивы
```

### Компоненты

#### 1. Сервисы

**ProductService** - HTTP клиент для работы с API:
- Методы для всех CRUD операций
- Обработка ошибок HTTP запросов
- Возврат типизированных результатов

**ThemeService** - управление темами оформления:
- Переключение между светлой и темной темами
- Сохранение выбора в localStorage
- Применение темы через data-bs-theme атрибут

#### 2. Страницы

**Home.razor** - главная страница:
- Обзор системы
- Карточки с описанием функций
- Быстрая навигация

**Products.razor** - список товаров:
- Таблица с данными товаров
- Поиск по названию и описанию
- Фильтрация по категориям
- Кнопки редактирования и удаления
- Модальное окно подтверждения удаления

**AddProduct.razor** - добавление товара:
- Форма с валидацией
- Обработка ошибок
- Индикаторы загрузки

**EditProduct.razor** - редактирование товара:
- Предзаполненная форма
- Загрузка данных по ID
- Обновление товара

#### 3. Компоненты UI

**NavMenu.razor** - навигационное меню:
- Ссылки на основные страницы
- Переключатель тем
- Responsive дизайн

**Alert.razor** - универсальный компонент уведомлений:
- Поддержка всех типов Bootstrap alert
- Возможность закрытия
- Настраиваемые иконки и заголовки

**LoadingSpinner.razor** - индикатор загрузки:
- Настраиваемый размер и цвет
- Текст загрузки
- Центрирование

## Взаимодействие компонентов

### Поток данных
1. **Клиент** отправляет HTTP запрос через ProductService
2. **Сервер** получает запрос в ProductsController
3. **Контроллер** обращается к ApplicationDbContext
4. **EF Core** выполняет запрос к SQLite базе данных
5. **Результат** возвращается обратно по цепочке
6. **Клиент** обновляет UI на основе полученных данных

### Обработка ошибок
- **Сервер**: Try-catch в контроллере → HTTP статус-коды
- **Клиент**: Проверка успешности запроса → Отображение ошибок в UI

### Валидация
- **Клиент**: Data Annotations + EditForm → Валидация в реальном времени
- **Сервер**: ModelState.IsValid → Возврат ошибок валидации

## Безопасность

### HTTPS
- Принудительное использование HTTPS в production
- Настройка сертификатов для разработки

### CORS
- Ограничение доступа только с клиентских доменов
- Настройка разрешенных методов и заголовков

### Валидация
- Двойная валидация (клиент + сервер)
- Защита от некорректных данных

## Производительность

### Клиент
- Blazor WebAssembly для быстрого UI
- Минимальные HTTP запросы
- Кэширование в браузере

### Сервер
- Async/await для всех операций
- Entity Framework с оптимизированными запросами
- SQLite для быстрого доступа к данным

## Масштабируемость

### Горизонтальное масштабирование
- Stateless API сервер
- Возможность добавления load balancer
- Разделение клиента и сервера

### Вертикальное масштабирование
- Возможность замены SQLite на SQL Server/PostgreSQL
- Добавление кэширования (Redis)
- Оптимизация запросов к базе данных

## Развитие системы

### Возможные улучшения
1. **Аутентификация и авторизация**
2. **Пагинация для больших списков**
3. **Загрузка изображений товаров**
4. **Экспорт данных**
5. **Уведомления в реальном времени (SignalR)**
6. **Многоязычность**
7. **Аудит изменений**
8. **API версионирование**
