# 🐘 Настройка PostgreSQL для системы управления товарами

## 🎯 Обзор

Приложение теперь использует PostgreSQL вместо SQLite для лучшей производительности и функциональности.

## 🚀 Быстрый старт

### Вариант 1: Docker (рекомендуется) 🐳

**Преимущества:**
- ✅ Не требует установки PostgreSQL на систему
- ✅ Изолированная среда
- ✅ Легко удалить после использования
- ✅ Включает pgAdmin для управления

**Шаги:**
1. Установите Docker Desktop
2. Запустите скрипт:
   ```powershell
   .\start-********.ps1
   ```
3. Дождитесь сообщения "PostgreSQL готов к работе!"

### Вариант 2: Локальная установка 💻

**Преимущества:**
- ✅ Не требует Docker
- ✅ Прямой доступ к PostgreSQL
- ✅ Лучшая производительность

**Шаги:**
1. Скачайте PostgreSQL с https://www.********ql.org/download/
2. Установите с настройками по умолчанию
3. Запустите скрипт:
   ```powershell
   .\setup-********ql.ps1
   ```

## 📋 Требования

### Для Docker варианта:
- Windows 10/11 с WSL2
- Docker Desktop
- 4 GB свободной RAM

### Для локальной установки:
- Windows 10/11
- PostgreSQL 12 или новее
- 2 GB свободного места

## 🔧 Ручная настройка

Если автоматические скрипты не работают:

### 1. Установка PostgreSQL

**Windows:**
```powershell
# Через Chocolatey
choco install ********ql

# Через Scoop
scoop install ********ql

# Или скачайте с официального сайта
```

**Docker:**
```bash
docker run --name ******** \
  -e POSTGRES_DB=ProductManagement \
  -e POSTGRES_USER=******** \
  -e POSTGRES_PASSWORD=******** \
  -p 5432:5432 \
  -d ********:16
```

### 2. Создание базы данных

```sql
-- Подключитесь к PostgreSQL
psql -h localhost -U ********

-- Создайте базу данных
CREATE DATABASE "ProductManagement";

-- Выйдите
\q
```

### 3. Настройка приложения

Убедитесь, что в `appsettings.json` правильная строка подключения:
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Host=localhost;Database=ProductManagement;Username=********;Password=********;Port=5432"
  }
}
```

### 4. Применение миграций

```powershell
cd BlazorServer
dotnet ef migrations add InitialCreate
dotnet ef database update
```

## 🔍 Проверка установки

### 1. Проверка подключения
```powershell
# Для Docker
docker exec -it product-management-db psql -U ******** -d ProductManagement

# Для локальной установки
psql -h localhost -U ******** -d ProductManagement
```

### 2. Проверка таблиц
```sql
-- Список таблиц
\dt

-- Проверка данных
SELECT COUNT(*) FROM "Products";
```

### 3. Проверка приложения
```powershell
# Запустите сервер
cd BlazorServer
dotnet run

# Проверьте API
curl https://localhost:7297/api/products
```

## 🛠️ Управление PostgreSQL

### Docker команды
```powershell
# Запуск
docker-compose up -d

# Остановка
docker-compose down

# Просмотр логов
docker-compose logs ********

# Подключение к базе
docker exec -it product-management-db psql -U ******** -d ProductManagement
```

### Локальные команды
```powershell
# Статус службы
Get-Service ********ql*

# Запуск службы
Start-Service ********ql-x64-*

# Остановка службы
Stop-Service ********ql-x64-*

# Подключение к базе
psql -h localhost -U ******** -d ProductManagement
```

## 🌐 pgAdmin (веб-интерфейс)

При использовании Docker автоматически запускается pgAdmin:

**Доступ:**
- URL: http://localhost:8080
- Email: <EMAIL>
- Пароль: admin

**Настройка подключения к серверу:**
1. Кликните "Add New Server"
2. Заполните:
   - Name: ProductManagement
   - Host: ******** (для Docker) или localhost
   - Port: 5432
   - Username: ********
   - Password: ********

## 📊 Мониторинг и обслуживание

### Проверка производительности
```sql
-- Размер базы данных
SELECT pg_size_pretty(pg_database_size('ProductManagement'));

-- Статистика таблиц
SELECT schemaname, tablename, n_tup_ins, n_tup_upd, n_tup_del 
FROM pg_stat_user_tables;

-- Активные подключения
SELECT count(*) FROM pg_stat_activity;
```

### Резервное копирование
```powershell
# Создание бэкапа
pg_dump -h localhost -U ******** ProductManagement > backup.sql

# Восстановление
psql -h localhost -U ******** ProductManagement < backup.sql
```

## 🚨 Устранение проблем

### PostgreSQL не запускается
```powershell
# Проверьте порт
netstat -an | findstr 5432

# Проверьте логи Docker
docker-compose logs ********

# Перезапустите контейнер
docker-compose restart ********
```

### Ошибки подключения
```powershell
# Проверьте настройки брандмауэра
New-NetFirewallRule -DisplayName "PostgreSQL" -Direction Inbound -Port 5432 -Protocol TCP -Action Allow

# Проверьте строку подключения
dotnet user-secrets set "ConnectionStrings:DefaultConnection" "Host=localhost;Database=ProductManagement;Username=********;Password=********;Port=5432"
```

### Проблемы с миграциями
```powershell
# Удалите миграции
Remove-Item -Recurse BlazorServer\Migrations

# Пересоздайте
cd BlazorServer
dotnet ef migrations add InitialCreate
dotnet ef database update
```

## 📈 Оптимизация

### Настройки производительности
```sql
-- Увеличение shared_buffers (в ********ql.conf)
shared_buffers = 256MB

-- Настройка work_mem
work_mem = 4MB

-- Включение автовакуума
autovacuum = on
```

### Индексы для приложения
```sql
-- Индекс для поиска по названию
CREATE INDEX idx_products_name ON "Products" USING gin(to_tsvector('russian', "Name"));

-- Индекс для фильтрации по категории
CREATE INDEX idx_products_category ON "Products"("Category");

-- Индекс для сортировки по дате
CREATE INDEX idx_products_created_date ON "Products"("CreatedDate");
```

## ✅ Контрольный список

- [ ] PostgreSQL установлен и запущен
- [ ] База данных ProductManagement создана
- [ ] Пользователь ******** настроен (пароль: ********)
- [ ] Порт 5432 доступен
- [ ] Миграции применены успешно
- [ ] Тестовые данные загружены
- [ ] Приложение подключается к базе
- [ ] pgAdmin доступен (для Docker)

## 🎉 Готово!

После успешной настройки PostgreSQL вы можете:

1. **Запустить приложение:**
   ```powershell
   .\start-app.ps1
   ```

2. **Открыть в браузере:**
   - Приложение: https://localhost:7084
   - pgAdmin: http://localhost:8080

3. **Проверить API:**
   - https://localhost:7297/api/products

Приложение теперь работает с PostgreSQL! 🚀
